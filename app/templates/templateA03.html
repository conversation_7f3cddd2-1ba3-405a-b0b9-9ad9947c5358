<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{ resume.basicInfo.name | default("我的") }}的简历</title>
  <link rel="stylesheet" href="{{ base_url|default('') }}/static/fontawesome/css/all.min.css" />
  <style>
    :root {
      --theme-color: {{ theme_color | default('#2E75B6') }};
      --base-font-size: {{ base_font_size|default(11) }}pt;
      --max-font-size: {{ max_font_size|default(15) }}pt;
      --spacing: {{ spacing|default(1.5) }};
      --text-color: #000000;
      --secondary-text-color: #555555;
      --border-color: #e0e0e0;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      /* font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; */
      /* font-family: 'Alibaba PuHuiTi', 'Noto Sans SC'; */
    }

    body {
      background-color: #f0f0f0;
      display: flex;
      justify-content: center;
      padding: 0px 0;
      overflow-x: hidden;
      font-family: "Source Han Sans SC VF", sans-serif;
    }

    .resume-container {
      width: 210mm;
      /* min-height: 297mm; */
      background-color: white;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
    }

    /* Header Section */
    .resume-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--theme-color);
      padding: 15px 30px;
      color: white;
    }

    .resume-header-title {
      font-size: calc(var(--base-font-size) * 2);
      font-weight: bold;
      letter-spacing: 2px;
    }

    .resume-header-icons {
      display: flex;
      gap: 20px;
    }

    .resume-header-icons i {
      font-size: calc(var(--base-font-size) * 1.8);
      display: inline-block !important;
      width: auto !important;
      height: auto !important;
    }

    /* Basic Info Section */
    .basic-info-section {
      padding: 20px 30px 0px 30px;
      display: grid;
      grid-template-columns: 1fr auto; /* Main content | Photo */
      gap: 25px;
      /* border-bottom: 1px solid var(--border-color); */
      align-items: start;
    }

    /* When no photo, use single column layout */
    .basic-info-section.no-photo {
      grid-template-columns: 1fr;
    }

    .basic-info-left {
      display: flex;
      flex-direction: column;
      gap: 15px;
      padding: 0px;
    }

    .basic-info-name h2 {
      display: flex;
      align-items: center;
      /* margin-bottom: 10px; */
    }

    .basic-info-name h2 span {
      font-size: calc(var(--base-font-size) *1.8);
      font-weight: bold;
      color: #000000;
    }
    .basic-info-name h2::after {
      content: "";
      flex-grow: 1;
      height: 1px;
      margin-left: 10px;
      background-color: var(--theme-color);
    }

    .basic-info-details {
      display: grid;
      grid-template-columns: repeat(2, minmax(200px, 1fr)); /* Responsive columns */
      gap: 8px 15px; /* Row and column gap */
      font-size: var(--base-font-size);
      color: var(--secondary-text-color);
      /* background-color: rgb(255, 255, 255); */
    }

    .basic-info-details p {
      display: flex;
      align-items: baseline;
    }

    .basic-info-details p span:first-child {
      font-weight: bold;
      min-width: 75px; /* Adjusted for labels like "政治面貌" */
      text-align: justify;
      color: var(--text-color);
      /* text-align: center; remove this to align left */
      background-color: rgb(255, 255, 255);
      margin-right: 5px; /* Add some space between label and value */
    }
     .basic-info-details p span:last-child {
       word-break: break-all;
     }

    .basic-info-photo {
      width: 110px;
      height: 154px;
      object-fit: cover;
      /* border: 1px solid var(--border-color); */
      align-self: center;
    }

    /* General Section Styling */
    .section {
      padding: 10px 20px;
      margin-bottom: 0px;
    }
    .section#name-header {
      padding: 15px 25px 0px 25px;
    }

    .section-header {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      background-color: rgb(255, 255, 255);
    }

    .section-header i {
      margin-right: 10px;
      width: 18px;
      font-size: calc(var(--base-font-size) * 1.1);
      color: #fff; /* Icon color inside title */
      display: inline-block; /* 确保图标正确显示 */
    }

    .section-title {
      display: flex;
      align-items: center;
      font-size: calc(var(--base-font-size) * 1.3);
      background-color: var(--theme-color);
      border-radius: 6px;
      padding: 6px 15px;
      color: #fff;
      margin-right:10px; /* Space between title and line */
    }

    /* 确保Font Awesome图标正确显示 */
    .section-title i {
      display: inline-block !important;
      width: auto !important;
      height: auto !important;
      margin-right: 8px;
    }
    .section-title h2 {
      font-weight: bold;
      font-size: calc(var(--base-font-size) * 1.3);
      color: #fff; /* Ensure h2 color is white */
    }

    .section-header-line { /* Renamed from ::after for clarity */
      flex-grow: 1;
      height: 1px;
      background-color: var(--theme-color);
    }

    .section-content {
      padding-left: 10px;
      background-color: rgb(255, 255, 255);
    }

    .section-item {
      margin-bottom: 5px;
    }
    .item-header {
      margin-bottom: 5px;
      padding: 0px 10px;      
    }

    .section-item .three-column {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 0px 10px;
      padding: 0px 0px;
      font-weight: bold;
    }
    .section-item .two-column {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0px 10px;
      padding: 0px 20px;
      font-weight: bold;
    }
    .item-header .date-range {
      color: var(--secondary-text-color);
      font-weight: normal;
      text-align: right; /* Align dates to the right */
    }

    .item-description p {
      color: var(--secondary-text-color);
      padding-left: 15px;
    }
    .item-description p strong {
        color: var(--text-color);
        margin-right: 5px;
    }

    .horizon-item-list {
        padding-left: 20px;
        display: flex;
        align-items: center;
        flex-wrap:wrap;
        gap: 0px 5px; /* Reduced gap */
    }
    .horizon-item-list li {
        margin-bottom: 10px; /* Add some bottom margin for wrapped items */
        background-color: #f0f0f0; /* Light background for items */
        /* padding: 0px 8px; */
        border-radius: 4px;
        font-size: calc(var(--base-font-size) * 0.95);
    }
    /* 关键：用伪元素在每个列表项后添加顿号，最后一个除外 */
    .horizon-item-list li:not(:last-child)::after {
      content: "、";          /* 添加中文顿号 */
    }

     .long-text-item {
        margin-bottom: 10px;
        line-height: var(--spacing);
     }
     .long-text-item .date-prefix {
         font-weight: bold;
         margin-right: 8px;
         color: var(--text-color);
     }
     .long-text-item p, .long-text-item span:not(.date-prefix) {
         color: var(--secondary-text-color);
     }

    .hidden {
      display: none;
    }

    @page {
      size: A4;
      margin: 0;
    }

    @media print {
      body {
        background-color: white;
        padding: 0;
      }
      .resume-container {
        box-shadow: none;
        margin: 0;
        width: 100%;
        min-height: 0;
      }
      .section {
        break-inside: avoid;
        page-break-inside: avoid;
      }
      .section-header {
        break-inside: avoid;
        page-break-inside: avoid;
      }
      .section-item, .long-text-item, .horizon-item-list li {
        break-inside: avoid;
        page-break-inside: avoid;
      }
      .item-description p {
        orphans: 3;
        widows: 3;
      }
    }

    p, h1, h2, h3, span, li, div {
       word-wrap: break-word;
       overflow-wrap: break-word;
       max-width: 100%;
    }

    /* 确保所有Font Awesome图标正确显示 */
    .fa, .fas, .far, .fal, .fab {
      display: inline-block !important;
      font-style: normal !important;
      font-variant: normal !important;
      text-rendering: auto !important;
      line-height: 1 !important;
    }
  </style>
</head>
<body>
  <div class="resume-container">

    <header class="resume-header">
      <span class="resume-header-title">个人简历</span>
      <div class="resume-header-icons">
        <i class="fas fa-building" aria-hidden="true"></i>
        <i class="fas fa-graduation-cap" aria-hidden="true"></i>
        <i class="fas fa-seedling" aria-hidden="true"></i>
      </div>
    </header>

    {% if resume.basicInfo and resume.basicInfo.name %}
    <div class="section" id="name-header">
      <div class="basic-info-name">
          <h2>
              <span>{{ resume.basicInfo.name | default('') }}</span>
          </h2>
      </div>
    </div>
    {% endif %}

    {% if resume.basicInfo %}
    <section class="basic-info-section{% if not resume.basicInfo.photoUrl %} no-photo{% endif %}">
      <div class="basic-info-left">
        <div class="basic-info-details">
          {% if resume.basicInfo.gender %}<p><span>性别:</span><span>{{ resume.basicInfo.gender }}</span></p>{% endif %}
          {% if resume.basicInfo.age %}<p><span>年龄:</span><span>{{ resume.basicInfo.age }}</span></p>{% endif %}
          {% if resume.basicInfo.phone %}<p><span>电话:</span><span>{{ resume.basicInfo.phone }}</span></p>{% endif %}
          {% if resume.basicInfo.email %}<p><span>邮箱:</span><span>{{ resume.basicInfo.email }}</span></p>{% endif %}
          {% if resume.basicInfo.city %}<p><span>现居:</span><span>{{ resume.basicInfo.city }}</span></p>{% endif %}
          {% if resume.basicInfo.educationLevel %}<p><span>学历:</span><span>{{ resume.basicInfo.educationLevel }}</span></p>{% endif %}
          {% if resume.basicInfo.nation %}<p><span>民族:</span><span>{{ resume.basicInfo.nation }}</span></p>{% endif %}
          {% if resume.basicInfo.hometown %}<p><span>籍贯:</span><span>{{ resume.basicInfo.hometown }}</span></p>{% endif %}
          {% if resume.basicInfo.politics %}<p><span>政治面貌:</span><span>{{ resume.basicInfo.politics }}</span></p>{% endif %}
          {% if resume.basicInfo.marriage %}<p><span>婚姻状况:</span><span>{{ resume.basicInfo.marriage }}</span></p>{% endif %}
          {% if resume.basicInfo.birthday %}<p><span>出生日期:</span><span>{{ resume.basicInfo.birthday }}</span></p>{% endif %}
          {% if resume.basicInfo.height %}<p><span>身高:</span><span>{{ resume.basicInfo.height }}</span></p>{% endif %}
          {% if resume.basicInfo.weight %}<p><span>体重:</span><span>{{ resume.basicInfo.weight }}</span></p>{% endif %}
          {% if resume.basicInfo.wechat %}<p><span>微信:</span><span>{{ resume.basicInfo.wechat }}</span></p>{% endif %}
          {% if resume.basicInfo.customTitle1 and resume.basicInfo.customContent1 %}<p><span>{{ resume.basicInfo.customTitle1 }}:</span><span>{{ resume.basicInfo.customContent1 }}</span></p>{% endif %}
          {% if resume.basicInfo.customTitle2 and resume.basicInfo.customContent2 %}<p><span>{{ resume.basicInfo.customTitle2 }}:</span><span>{{ resume.basicInfo.customContent2 }}</span></p>{% endif %}
        </div>
      </div>
      {% if resume.basicInfo.photoUrl %}
      <img class="basic-info-photo" src="{{ resume.basicInfo.photoUrl }}" alt="个人照片" loading="eager"
           onerror="this.onerror=null; this.style.display='none';">
      {% endif %}
    </section>
    {% endif %}

    {% for module in ordered_modules %}
      {% if module.key == 'jobIntention' and module.data and (module.data.position or module.data.city or module.data.salary or module.data.status) %}
      {# Job Intention details beyond basic info's position #}
      <section class="section" id="jobIntention-section">
        <div class="section-header">
          <div class="section-title">
            <i class="{{ module.icon | default('fas fa-bullseye') }}" aria-hidden="true"></i>
            <h2>{{ module.title | default('求职意向') }}</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
            <div class="basic-info-details">
                {% if module.data.position %}<p><span>期望职位:</span><span>{{ module.data.position }}</span></p>{% endif %}
                {% if module.data.city %}<p><span>期望城市:</span><span>{{ module.data.city }}</span></p>{% endif %}
                {% if module.data.salary %}<p><span>期望薪资:</span><span>{{ module.data.salary }}</span></p>{% endif %}
                {% if module.data.status %}<p><span>求职状态:</span><span>{{ module.data.status }}</span></p>{% endif %}
            </div>
        </div>
      </section>
      {% endif %}


      {% if module.key == 'education' and module.data %}
      <section class="section" id="education-section">
        <div class="section-header">
          <div class="section-title">
            <i class="{{ module.icon | default('fas fa-graduation-cap') }}" aria-hidden="true"></i>
            <h2>{{ module.title | default('教育背景') }}</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          {% for item in module.data %}
          <div class="section-item">
            <div class="item-header three-column">
              <span>{{ item.school }}</span>
              <span>{{ item.major }} ({{ item.degree }})</span>
              <span class="date-range">{{ item.startDate }} - {{ item.endDate }}</span>
            </div>
            {% if item.description %}
            <div class="item-description">
              <p>{{ item.description | safe }}</p>
            </div>
            {% endif %}
          </div>
          {% endfor %}
        </div>
      </section>
      {% endif %}

      {% if module.key == 'internship' and module.data %}
      <section class="section" id="internship-section">
        <div class="section-header">
          <div class="section-title">
            <i class="{{ module.icon | default('fas fa-id-badge') }}" aria-hidden="true"></i>
            <h2>{{ module.title | default('实习经历') }}</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          {% for item in module.data %}
          <div class="section-item">
            <div class="item-header three-column">
              <span>{{ item.company }}</span>
              <span>{{ item.position }}</span>
              <span class="date-range">{{ item.startDate }} - {{ item.endDate }}</span>
            </div>
            {% if item.content %} {# Schema uses 'content' for internship, sample_templateA03 uses description for general work #}
            <div class="item-description">
               <p>{{ item.content | safe }}</p>
            </div>
            {% endif %}
          </div>
          {% endfor %}
        </div>
      </section>
      {% endif %}

      {% if module.key == 'work' and module.data %}
      <section class="section" id="work-section">
        <div class="section-header">
          <div class="section-title">
            <i class="{{ module.icon | default('fas fa-briefcase') }}" aria-hidden="true"></i>
            <h2>{{ module.title | default('工作经历') }}</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          {% for item in module.data %}
          <div class="section-item">
            <div class="item-header three-column">
              <span>{{ item.company }}</span>
              <span>{{ item.position }}</span>
              <span class="date-range">{{ item.startDate }} - {{ item.endDate }}</span>
            </div>
            {% if item.description %}
            <div class="item-description">
              <p>{{ item.description | safe }}</p>
            </div>
            {% endif %}
          </div>
          {% endfor %}
        </div>
      </section>
      {% endif %}

      {% if module.key == 'project' and module.data %}
      <section class="section" id="project-section">
        <div class="section-header">
          <div class="section-title">
            <i class="{{ module.icon | default('fas fa-project-diagram') }}" aria-hidden="true"></i>
            <h2>{{ module.title | default('项目经历') }}</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          {% for item in module.data %}
          <div class="section-item">
            <div class="item-header three-column">
              <span>{{ item.projectName }}</span>
              <span>{{ item.role }}</span>
              <span class="date-range">{{ item.startDate }} - {{ item.endDate }}</span>
            </div>
            {% if item.description %}
            <div class="item-description">
              <p>{{ item.description | safe }}</p>
            </div>
            {% endif %}
          </div>
          {% endfor %}
        </div>
      </section>
      {% endif %}

      {% if module.key == 'school' and module.data %} {# school is for SchoolExperienceItem #}
      <section class="section" id="school-section">
        <div class="section-header">
          <div class="section-title">
            <i class="{{ module.icon | default('fas fa-school') }}" aria-hidden="true"></i>
            <h2>{{ module.title | default('在校经历') }}</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          {% for item in module.data %}
          <div class="section-item">
          <div class="item-header three-column"> {# Matching sample_templateA03 structure #}
            <span>{% if item.role %}{{item.role}}{% endif %}:</span>
            <span class="empty-span"></span>  
            <span class="date-range">{{ item.startDate }} - {{ item.endDate }}</span>
            <!-- <span class="date-prefix">{{ item.startDate }}{% if item.endDate and item.endDate != item.startDate and item.endDate != "至今" %}-{{ item.endDate }}{% elif item.endDate == "至今" %}-{{ item.endDate }}{% endif %}</span> -->
            <!-- <span>{{ item.content | safe }}</span> -->
          </div>
            {% if item.content %}
            <div class="item-description">
              <p>{{ item.content | safe }}</p>
            </div>
            {% endif %}
          </div>
          {% endfor %}
        </div>
      </section>
      {% endif %}

      {% if module.key == 'skills' and module.data %}
      <section class="section" id="skills-section">
        <div class="section-header">
          <div class="section-title">
            <i class="{{ module.icon | default('fas fa-certificate') }}" aria-hidden="true"></i>
            <h2>{{ module.title | default('技能证书') }}</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          <ul class="horizon-item-list">
            {% for skill in module.data %}
            <li>{{ skill }}</li>
            {% endfor %}
          </ul>
        </div>
      </section>
      {% endif %}

      {% if module.key == 'awards' and module.data %}
      <section class="section" id="awards-section">
        <div class="section-header">
          <div class="section-title">
            <i class="{{ module.icon | default('fas fa-award') }}" aria-hidden="true"></i>
            <h2>{{ module.title | default('奖项荣誉') }}</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          <ul class="horizon-item-list">
            {% for award in module.data %}
            <li>{{ award }}</li>
            {% endfor %}
          </ul>
        </div>
      </section>
      {% endif %}

      {% if module.key == 'interests' and module.data %}
      <section class="section" id="interests-section">
        <div class="section-header">
          <div class="section-title">
            <i class="{{ module.icon | default('fas fa-heart') }}" aria-hidden="true"></i>
            <h2>{{ module.title | default('兴趣爱好') }}</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          <ul class="horizon-item-list">
            {% for interest in module.data %}
            <li>{{ interest }}</li>
            {% endfor %}
          </ul>
        </div>
      </section>
      {% endif %}

      {% if module.key == 'evaluation' and module.data %}
      <section class="section" id="evaluation-section">
        <div class="section-header">
          <div class="section-title">
            <i class="{{ module.icon | default('fas fa-comment') }}" aria-hidden="true"></i>
            <h2>{{ module.title | default('自我评价') }}</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          {% for eval_item in module.data %} {# Assuming evaluation is List[Dict[str, Any]] with 'content' key #}
          <div class="item-description">
            <p>{{ eval_item.content | safe if eval_item.content else eval_item | safe }}</p> {# Handle if item itself is a string #}
          </div>
          {% endfor %}
        </div>
      </section>
      {% endif %}

      {% if module.key == 'custom1' and module.data %}
      <section class="section" id="custom1-section">
        <div class="section-header">
            <div class="section-title">
                <i class="{{ module.icon | default('fas fa-star') }}" aria-hidden="true"></i>
                <h2>{{ module.data[0].customName | default('自定义模块1') }}</h2>
            </div>
            <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          {% for item in module.data %}
            <div class="section-item">
               <div class="item-header two-column">
                 <span>{% if item.role %} {{ item.role }}{% endif %}</span>
                 <span class="date-range">{{ item.startDate }}{% if item.endDate %} - {{ item.endDate }}{% endif %}</span>
               </div>
               {% if item.content %}
                 <div class="item-description">
                    <p>{{ item.content | safe }}</p>
                 </div>
               {% endif %}
            </div>
          {% endfor %}
        </div>
      </section>
      {% endif %}

      {% if module.key == 'custom2' and module.data %}
      <section class="section" id="custom2-section">
        <div class="section-header">
            <div class="section-title">
                <i class="{{ module.icon | default('fas fa-star') }}" aria-hidden="true"></i>
                <h2>{{ module.data[0].customName | default('自定义模块2') }}</h2>
            </div>
            <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          {% for item in module.data %}
            <div class="section-item">
               <div class="item-header two-column">
                 <span>{% if item.role %} {{ item.role }}{% endif %}</span>
                 <span class="date-range">{{ item.startDate }}{% if item.endDate %} - {{ item.endDate }}{% endif %}</span>
               </div>
               {% if item.content %}
                 <div class="item-description">
                    <p>{{ item.content | safe }}</p>
                 </div>
               {% endif %}
            </div>
          {% endfor %}
        </div>
      </section>
      {% endif %}

      {% if module.key == 'custom3' and module.data %}
      <section class="section" id="custom3-section">
        <div class="section-header">
            <div class="section-title">
                <i class="{{ module.icon | default('fas fa-star') }}" aria-hidden="true"></i>
                <h2>{{ module.data[0].customName | default('自定义模块3') }}</h2>
            </div>
            <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          {% for item in module.data %}
            <div class="section-item">
               <div class="item-header two-column">
                 <span>{% if item.role %} {{ item.role }}{% endif %}</span>
                 <span class="date-range">{{ item.startDate }}{% if item.endDate %} - {{ item.endDate }}{% endif %}</span>
               </div>
               {% if item.content %}
                 <div class="item-description">
                    <p>{{ item.content | safe }}</p>
                 </div>
               {% endif %}
            </div>
          {% endfor %}
        </div>
      </section>
      {% endif %}

    {% endfor %}
  </div>
</body>
</html>
