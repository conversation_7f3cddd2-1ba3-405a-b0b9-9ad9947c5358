#!/usr/bin/env python3
"""
生产环境一键部署脚本
自动化执行完整的生产环境部署流程
"""
import os
import sys
import subprocess
import logging
import argparse
import json
from datetime import datetime
import mysql.connector
from mysql.connector import Error

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 部署配置
DEPLOY_CONFIG = {
    'database': {
        'host': 'localhost',
        'database': 'resume_service',
        'user': 'resume_user',
        'password': 'Resume123!@#Prod'  # 生产环境密码
    },
    'backup_dir': 'data_backups',
    'log_dir': 'deploy_logs'
}

class ProductionDeployer:
    def __init__(self, config):
        self.config = config
        self.deploy_log = []
        self.ensure_log_directory()
    
    def ensure_log_directory(self):
        """确保日志目录存在"""
        if not os.path.exists(self.config['log_dir']):
            os.makedirs(self.config['log_dir'])
    
    def log_step(self, step, status, message=""):
        """记录部署步骤"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'step': step,
            'status': status,
            'message': message
        }
        self.deploy_log.append(log_entry)
        
        status_symbol = "✅" if status == "success" else "❌" if status == "error" else "⚠️"
        logger.info(f"{status_symbol} {step}: {message}")
    
    def save_deploy_log(self):
        """保存部署日志"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = os.path.join(self.config['log_dir'], f"deploy_log_{timestamp}.json")
        
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump({
                'deploy_time': datetime.now().isoformat(),
                'steps': self.deploy_log
            }, f, ensure_ascii=False, indent=2)
        
        logger.info(f"部署日志已保存: {log_file}")
    
    def check_prerequisites(self):
        """检查部署前置条件"""
        logger.info("检查部署前置条件...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            self.log_step("检查Python版本", "error", f"需要Python 3.8+，当前版本: {sys.version}")
            return False
        self.log_step("检查Python版本", "success", f"Python版本: {sys.version}")
        
        # 检查必要的Python包
        required_packages = ['mysql-connector-python', 'fastapi', 'uvicorn']
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                self.log_step(f"检查包 {package}", "success", "已安装")
            except ImportError:
                self.log_step(f"检查包 {package}", "error", "未安装")
                return False
        
        # 检查MySQL连接
        try:
            connection = mysql.connector.connect(**self.config['database'])
            connection.close()
            self.log_step("检查数据库连接", "success", "连接正常")
        except Error as e:
            self.log_step("检查数据库连接", "error", str(e))
            return False
        
        # 检查必要的脚本文件
        required_scripts = [
            'migrate_complete_database.py',
            'export_data_backup.py',
            'import_data_backup.py'
        ]
        for script in required_scripts:
            if not os.path.exists(script):
                self.log_step(f"检查脚本 {script}", "error", "文件不存在")
                return False
            self.log_step(f"检查脚本 {script}", "success", "文件存在")
        
        return True
    
    def update_database_config(self):
        """更新数据库配置文件"""
        logger.info("更新数据库配置...")
        
        config_files = [
            'migrate_complete_database.py',
            'export_data_backup.py',
            'import_data_backup.py',
            'migrate_error_report_update.py'
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    # 读取文件内容
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 替换数据库配置
                    old_config = "'password': 'Resume123!'"
                    new_config = f"'password': '{self.config['database']['password']}'"
                    content = content.replace(old_config, new_config)
                    
                    # 写回文件
                    with open(config_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.log_step(f"更新配置 {config_file}", "success", "配置已更新")
                except Exception as e:
                    self.log_step(f"更新配置 {config_file}", "error", str(e))
                    return False
        
        return True
    
    def create_database_structure(self):
        """创建数据库结构"""
        logger.info("创建数据库结构...")
        
        try:
            result = subprocess.run([
                sys.executable, 'migrate_complete_database.py'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_step("创建数据库结构", "success", "所有表创建完成")
                return True
            else:
                self.log_step("创建数据库结构", "error", result.stderr)
                return False
        except Exception as e:
            self.log_step("创建数据库结构", "error", str(e))
            return False
    
    def export_development_data(self):
        """导出开发环境数据"""
        logger.info("导出开发环境数据...")
        
        # 临时切换到开发环境配置
        dev_config = {
            'host': 'localhost',
            'database': 'resume_service',
            'user': 'resume_user',
            'password': 'Resume123!'
        }
        
        # 更新导出脚本配置
        export_script = 'export_data_backup.py'
        try:
            with open(export_script, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 临时替换配置
            content_backup = content
            for key, value in dev_config.items():
                content = content.replace(
                    f"'{key}': '{self.config['database'][key]}'",
                    f"'{key}': '{value}'"
                )
            
            with open(export_script, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 执行导出
            result = subprocess.run([
                sys.executable, export_script
            ], capture_output=True, text=True)
            
            # 恢复配置
            with open(export_script, 'w', encoding='utf-8') as f:
                f.write(content_backup)
            
            if result.returncode == 0:
                self.log_step("导出开发环境数据", "success", "数据导出完成")
                return True
            else:
                self.log_step("导出开发环境数据", "error", result.stderr)
                return False
                
        except Exception as e:
            self.log_step("导出开发环境数据", "error", str(e))
            return False
    
    def import_production_data(self):
        """导入生产环境数据"""
        logger.info("导入生产环境数据...")
        
        # 查找最新的备份文件
        backup_dir = self.config['backup_dir']
        if not os.path.exists(backup_dir):
            self.log_step("导入生产环境数据", "warning", "备份目录不存在，跳过数据导入")
            return True
        
        # 查找free_templates和resume_thumbs的备份文件
        tables_to_import = ['free_templates', 'resume_thumbs']
        imported_tables = []
        
        for table_name in tables_to_import:
            # 查找最新的JSON备份文件
            json_files = [f for f in os.listdir(backup_dir) 
                         if f.startswith(table_name) and f.endswith('.json')]
            
            if not json_files:
                self.log_step(f"导入 {table_name}", "warning", "未找到备份文件")
                continue
            
            # 选择最新的文件
            latest_file = sorted(json_files)[-1]
            file_path = os.path.join(backup_dir, latest_file)
            
            try:
                result = subprocess.run([
                    sys.executable, 'import_data_backup.py',
                    '--file', file_path,
                    '--table', table_name,
                    '--clear'
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log_step(f"导入 {table_name}", "success", f"从 {latest_file} 导入成功")
                    imported_tables.append(table_name)
                else:
                    self.log_step(f"导入 {table_name}", "error", result.stderr)
                    
            except Exception as e:
                self.log_step(f"导入 {table_name}", "error", str(e))
        
        if imported_tables:
            self.log_step("导入生产环境数据", "success", f"成功导入: {', '.join(imported_tables)}")
        else:
            self.log_step("导入生产环境数据", "warning", "没有成功导入任何表")
        
        return True
    
    def verify_deployment(self):
        """验证部署结果"""
        logger.info("验证部署结果...")
        
        try:
            connection = mysql.connector.connect(**self.config['database'])
            cursor = connection.cursor()
            
            # 检查所有表是否存在
            expected_tables = [
                'users', 'user_actions', 'feedback', 'feedback_replies',
                'free_templates', 'resume_thumbs', 'error_reports'
            ]
            
            cursor.execute(f"SHOW TABLES FROM {self.config['database']['database']}")
            existing_tables = [table[0] for table in cursor.fetchall()]
            
            missing_tables = set(expected_tables) - set(existing_tables)
            if missing_tables:
                self.log_step("验证表结构", "error", f"缺少表: {', '.join(missing_tables)}")
                return False
            
            self.log_step("验证表结构", "success", "所有表都存在")
            
            # 检查关键表的数据
            for table in ['free_templates', 'resume_thumbs']:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                self.log_step(f"验证 {table} 数据", "success", f"{count} 条记录")
            
            cursor.close()
            connection.close()
            
            return True
            
        except Exception as e:
            self.log_step("验证部署结果", "error", str(e))
            return False
    
    def deploy(self, skip_data_export=False, skip_data_import=False):
        """执行完整部署流程"""
        logger.info("=" * 60)
        logger.info("开始生产环境部署")
        logger.info("=" * 60)
        
        # 检查前置条件
        if not self.check_prerequisites():
            logger.error("前置条件检查失败，部署终止")
            return False
        
        # 更新数据库配置
        if not self.update_database_config():
            logger.error("更新数据库配置失败，部署终止")
            return False
        
        # 导出开发环境数据
        if not skip_data_export:
            if not self.export_development_data():
                logger.error("导出开发环境数据失败，部署终止")
                return False
        
        # 创建数据库结构
        if not self.create_database_structure():
            logger.error("创建数据库结构失败，部署终止")
            return False
        
        # 导入生产环境数据
        if not skip_data_import:
            if not self.import_production_data():
                logger.warning("导入生产环境数据失败，但继续部署")
        
        # 验证部署结果
        if not self.verify_deployment():
            logger.error("部署验证失败")
            return False
        
        logger.info("=" * 60)
        logger.info("生产环境部署完成！")
        logger.info("=" * 60)
        
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生产环境一键部署脚本')
    parser.add_argument('--skip-export', action='store_true', help='跳过数据导出步骤')
    parser.add_argument('--skip-import', action='store_true', help='跳过数据导入步骤')
    parser.add_argument('--config', help='自定义配置文件路径')
    
    args = parser.parse_args()
    
    # 加载配置
    config = DEPLOY_CONFIG
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            custom_config = json.load(f)
            config.update(custom_config)
    
    # 创建部署器
    deployer = ProductionDeployer(config)
    
    try:
        # 执行部署
        success = deployer.deploy(
            skip_data_export=args.skip_export,
            skip_data_import=args.skip_import
        )
        
        # 保存部署日志
        deployer.save_deploy_log()
        
        if success:
            logger.info("部署成功完成！")
            sys.exit(0)
        else:
            logger.error("部署失败！")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("部署被用户中断")
        deployer.save_deploy_log()
        sys.exit(1)
    except Exception as e:
        logger.exception("部署过程中发生异常")
        deployer.save_deploy_log()
        sys.exit(1)

if __name__ == "__main__":
    main()
